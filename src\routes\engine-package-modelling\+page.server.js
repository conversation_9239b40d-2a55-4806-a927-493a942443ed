import { error } from '@sveltejs/kit';
import { getDatabase } from '$lib/mongodb.js';
import { ObjectId } from 'mongodb';

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  try {
    const computerId = url.searchParams.get('computerId');
    const productDesignation = url.searchParams.get('productDesignation');

    if (!computerId) {
      throw error(400, 'Computer ID is required');
    }

    // Validate ObjectId format
    if (!ObjectId.isValid(computerId)) {
      throw error(400, 'Invalid Computer ID format');
    }

    console.log('Loading Engine Package Modelling data for:', { computerId, productDesignation });

    const db = await getDatabase();

    // Get computer information
    const computer = await db.collection('Computers').findOne({
      _id: new ObjectId(computerId)
    });

    if (!computer) {
      throw error(404, 'Computer not found');
    }

    // Get customer information
    const customerId = computer.customerId instanceof ObjectId
      ? computer.customerId
      : new ObjectId(computer.customerId);

    const customer = await db.collection('Customers').findOne({
      _id: customerId
    });

    if (!customer) {
      throw error(404, 'Customer not found');
    }

    // Extract computer category from model (e.g., "D16-B-GEN-10" -> "D16")
    const computerModel = computer.model || '';
    const computerCategory = computerModel.match(/^([A-Z]+\d+)/)?.[1] || '';

    console.log('Computer details:', {
      model: computerModel,
      category: computerCategory,
      productDesignation: productDesignation
    });

    // Get Engine Package Modelling data
    const enginePackageData = await db.collection('EnginePackageModelling').find({}).toArray();
    console.log(`Found ${enginePackageData.length} Engine Package Modelling records`);

    // Get Engine Variants data
    const engineVariants = await db.collection('EngineVariants').find({}).toArray();
    console.log(`Found ${engineVariants.length} Engine Variants records`);

    // Get Engine Package data
    const enginePackages = await db.collection('EnginePackage').find({}).toArray();
    console.log(`Found ${enginePackages.length} Engine Package records`);

    // Get Business Models data
    const businessModels = await db.collection('BusinessModels').find({}).toArray();
    console.log(`Found ${businessModels.length} Business Models records`);

    // Get Business Functions data
    const businessFunctions = await db.collection('BusinessFunctions').find({}).toArray();
    console.log(`Found ${businessFunctions.length} Business Functions records`);

    // Get Calculations data
    const calculations = await db.collection('Calculations').find({}).toArray();
    console.log(`Found ${calculations.length} Calculations records`);

    // Get Product Designation data
    const productDesignationData = await db.collection('ProductDesignation').find({
      ProductDesignation: productDesignation
    }).toArray();
    console.log(`Found ${productDesignationData.length} Product Designation records for ${productDesignation}`);

    // Get Service Types data
    const serviceTypes = await db.collection('ServiceTypes').find({}).toArray();
    console.log(`Found ${serviceTypes.length} Service Types records`);

    // Get Packages data
    const packages = await db.collection('Packages').find({}).toArray();
    console.log(`Found ${packages.length} Packages records`);

    return {
      computer: {
        ...computer,
        _id: computer._id.toString(),
        customerId: computer.customerId instanceof ObjectId
          ? computer.customerId.toString()
          : computer.customerId
      },
      customer: {
        ...customer,
        _id: customer._id.toString()
      },
      computerCategory,
      productDesignation: productDesignation || '',
      enginePackageData,
      engineVariants,
      enginePackages,
      businessModels,
      businessFunctions,
      calculations,
      productDesignationData,
      serviceTypes,
      packages,
      dataSource: {
        enginePackageModelling: enginePackageData.length,
        engineVariants: engineVariants.length,
        enginePackages: enginePackages.length,
        businessModels: businessModels.length,
        businessFunctions: businessFunctions.length,
        calculations: calculations.length,
        productDesignation: productDesignationData.length,
        serviceTypes: serviceTypes.length,
        packages: packages.length
      }
    };
  } catch (err) {
    console.error('Error loading Engine Package Modelling data:', err);
    throw error(500, 'Failed to load Engine Package Modelling data');
  }
}
